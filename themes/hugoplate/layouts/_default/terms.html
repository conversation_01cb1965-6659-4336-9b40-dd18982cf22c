{{ define "main" }}
  {{ partial "page-header" . }}


  <section class="section">
    <div class="container">
      <ul class="text-center">
        {{/* categories */}}
        {{ if eq .<PERSON><PERSON><PERSON> (`categories/` | absLangURL) }}
          {{ range site.Taxonomies.categories.ByCount }}
            <li class="m-3 inline-block">
              <a
                href="{{ .Page.Permalink }}"
                class="bg-light text-text-dark dark:bg-darkmode-light dark:text-darkmode-text-dark block rounded px-4 py-2 text-xl">
                {{ .Page.Title }}
                <span class="bg-body dark:bg-darkmode-body ml-2 rounded px-2">
                  {{ .Count }}
                </span>
              </a>
            </li>
          {{ end }}
        {{ end }}
        {{/* tags */}}
        {{ if eq .Permalink (`tags/` | absLangURL) }}
          {{ range site.Taxonomies.tags.ByCount }}
            <li class="m-3 inline-block">
              <a
                href="{{ .Page.Permalink }}"
                class="bg-light text-text-dark dark:bg-darkmode-light dark:text-darkmode-text-dark block rounded px-4 py-2 text-xl">
                {{ .Page.Title }}
                <span class="bg-body dark:bg-darkmode-body ml-2 rounded px-2">
                  {{ .Count }}
                </span>
              </a>
            </li>
          {{ end }}
        {{ end }}
      </ul>
    </div>
  </section>
{{ end }}
