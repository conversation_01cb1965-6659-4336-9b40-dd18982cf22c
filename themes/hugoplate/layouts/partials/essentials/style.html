<!-- DNS preconnect -->
<meta http-equiv="x-dns-prefetch-control" content="on" />
<link rel="preconnect" href="https://use.fontawesome.com" crossorigin />
<link rel="preconnect" href="//cdnjs.cloudflare.com" />
<link rel="preconnect" href="//www.googletagmanager.com" />
<link rel="preconnect" href="//www.google-analytics.com" />
<link rel="dns-prefetch" href="https://use.fontawesome.com" />
<link rel="dns-prefetch" href="//ajax.googleapis.com" />
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />
<link rel="dns-prefetch" href="//www.googletagmanager.com" />
<link rel="dns-prefetch" href="//www.google-analytics.com" />
<link rel="dns-prefetch" href="//fonts.googleapis.com" />
<link rel="dns-prefetch" href="//connect.facebook.net" />
<link rel="dns-prefetch" href="//platform.linkedin.com" />
<link rel="dns-prefetch" href="//platform.twitter.com" />

<!-- google fonts -->
{{ $pf:= site.Data.theme.fonts.font_family.primary }}
{{ $sf:= site.Data.theme.fonts.font_family.secondary }}
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<script>
  (function () {
    const googleFont = document.createElement("link");
    googleFont.href = "https://fonts.googleapis.com/css2?family={{$pf | safeURL}}{{with $sf}}&family={{. | safeURL}}{{end}}&display=swap";
    googleFont.type = "text/css";
    googleFont.rel = "stylesheet";
    document.head.appendChild(googleFont);
  })();
</script>

<!-- main styles -->
{{ $styles := slice }}
{{ $stylesLazy := slice }}

{{ range site.Params.plugins.css }}
  {{ if findRE "^http" .link }}
    <link
      crossorigin="anonymous"
      media="all"
      rel="stylesheet"
      href="{{ .link | relURL }}"
      {{ .attributes | safeHTMLAttr }} />
  {{ else }}
    {{ if not .lazy }}
      {{ $styles = $styles | append (resources.Get .link) }}
    {{ else }}
      {{ $stylesLazy = $stylesLazy | append (resources.Get .link) }}
    {{ end }}
  {{ end }}
{{ end }}

{{ $mainCSS := resources.Get "css/main.css" }}
{{ $tailwindOpts := dict "inlineImports" true }}
{{ $tailwindCSS := $mainCSS | css.TailwindCSS $tailwindOpts }}

{{ $styles = $styles | append $tailwindCSS }}
{{ $styles = $styles | resources.Concat "css/style.css" }}

{{ $stylesLazy = $stylesLazy | resources.Concat "css/style-lazy.css" }}

{{ if hugo.IsProduction }}
  {{ $styles = $styles | minify | fingerprint }}
  {{ $stylesLazy = $stylesLazy | minify | fingerprint }}
{{ end }}


<!-- link main style -->
<link
  href="{{ $styles.RelPermalink }}"
  integrity="{{ $styles.Data.Integrity }}"
  rel="stylesheet" />

<!-- link lazy style -->
<link
  defer
  async
  rel="stylesheet"
  href="{{ $stylesLazy.RelPermalink }}"
  integrity="{{ $stylesLazy.Data.Integrity }}"
  media="print"
  onload="this.media='all'; this.onload=null;" />
