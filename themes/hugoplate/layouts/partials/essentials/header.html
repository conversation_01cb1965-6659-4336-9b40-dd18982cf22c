<header
  class="header {{ if site.Params.navbar_fixed }}sticky top-0{{ end }} z-30">
  <nav class="navbar container">
    <!-- logo -->
    <div class="order-0">
      <!-- navbar brand/logo -->
      <a class="navbar-brand block" href="{{ site.Home.RelPermalink }}">
        {{ partial "logo" }}
      </a>
    </div>
    <!-- navbar toggler -->
    <input id="nav-toggle" type="checkbox" class="hidden" />
    <label
      for="nav-toggle"
      class="order-3 cursor-pointer flex items-center lg:hidden text-text-dark dark:text-white lg:order-1">
      <svg id="show-button" class="h-6 fill-current block" viewBox="0 0 20 20">
        <title>Menu Open</title>
        <path d="M0 3h20v2H0V3z m0 6h20v2H0V9z m0 6h20v2H0V0z"></path>
      </svg>
      <svg id="hide-button" class="h-6 fill-current hidden" viewBox="0 0 20 20">
        <title>Menu Close</title>
        <polygon
          points="11 9 22 9 22 11 11 11 11 22 9 22 9 11 -2 11 -2 9 9 9 9 -2 11 -2"
          transform="rotate(45 10 10)"></polygon>
      </svg>
    </label>
    <!-- /navbar toggler -->

    <!-- main navbar -->
    <ul
      id="nav-menu"
      class="navbar-nav order-3 hidden lg:flex w-full pb-6 lg:order-1 lg:w-auto lg:space-x-2 lg:pb-0 xl:space-x-8">
      {{ $currentPage := . }}
      {{ range site.Menus.main }}
        {{ $menuURL := .URL | absLangURL }}
        {{ $pageURL:= $currentPage.Permalink | absLangURL }}
        {{ $active := eq $menuURL $pageURL }}
        {{ if .HasChildren }}
          <li class="nav-item nav-dropdown group relative">
            <span
              class="nav-link {{ range .Children }}
                {{ $childURL := .URL | absLangURL }}
                {{ $active := eq $childURL $pageURL }}
                {{ if $active }}active{{ end }}
              {{ end }} inline-flex items-center">
              {{ .Name }}
              <svg class="h-4 w-4 fill-current" viewBox="0 0 20 20">
                <path
                  d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </span>
            <ul
              class="nav-dropdown-list lg:group-hover:visible lg:group-hover:opacity-100">
              {{ range .Children }}
                {{ $childURL := .URL | absLangURL }}
                {{ $active := eq $childURL $pageURL }}
                <li class="nav-dropdown-item">
                  <a
                    class="nav-dropdown-link {{ if $active }}
                      active
                    {{- end -}}"
                    {{ if findRE `^http` .URL }}
                      target="_blank" rel="noopener"
                    {{ end }}
                    href="{{- if findRE `^#` .URL -}}
                      {{- if not $.IsHome -}}
                        {{- site.Home.RelPermalink -}}
                      {{- end }}
                      {{- .URL -}}
                    {{- else -}}
                      {{- .URL | relLangURL -}}
                    {{- end -}}">
                    {{ .Name }}
                  </a>
                </li>
              {{ end }}
            </ul>
          </li>
        {{ else }}
          <li class="nav-item">
            <a
              class="nav-link {{ if $active }}active{{- end -}}"
              {{ if findRE `^http` .URL }}
                target="_blank" rel="noopener"
              {{ end }}
              href="{{- if findRE `^#` .URL -}}
                {{- if not $.IsHome -}}
                  {{- site.Home.RelPermalink -}}
                {{- end }}{{- .URL -}}
              {{- else -}}
                {{- .URL | relLangURL -}}
              {{- end -}}"
              >{{ .Name }}</a
            >
          </li>
        {{ end }}
      {{ end }}
      {{ if site.Params.navigation_button.enable }}
        <li class="mt-4 inline-block lg:hidden">
          <a
            class="btn btn-outline-primary btn-sm"
            href="{{ site.Params.navigation_button.link | relLangURL }}">
            {{ site.Params.navigation_button.label }}
          </a>
        </li>
      {{ end }}
    </ul>

    <div class="order-1 ml-auto flex items-center md:order-2 lg:ml-0">
      {{ with site.Params.search }}
        {{ if .enable }}
          <button
            aria-label="search"
            {{ if and (not site.Params.navigation_button.enable) (not site.Params.theme_switcher) }}
              class="border-border text-text-dark hover:text-primary
              dark:border-darkmode-border mr-5 inline-block border-r
              lg:border-r-0 pr-5 lg:pr-0 text-xl dark:text-white
              dark:hover:text-darkmode-primary"
            {{ else }}
              class="border-border text-text-dark hover:text-primary
              dark:border-darkmode-border mr-5 inline-block border-r pr-5
              text-xl dark:text-white dark:hover:text-darkmode-primary"
            {{ end }}
            data-target="search-modal">
            <i class="fa-solid fa-search"></i>
          </button>
        {{ end }}
      {{ end }}

      {{ partial "components/language-switcher" (dict "Context" . "Class" "mr-5 pl-2 py-1 dark:bg-darkmode-light rounded") }}
      {{ partial "components/theme-switcher" (dict "Class" "mr-5") }}


      <!-- navigation btn -->
      {{ if site.Params.navigation_button.enable }}
        <a
          href="{{ site.Params.navigation_button.link | relLangURL }}"
          class="btn btn-outline-primary btn-sm hidden lg:inline-block">
          {{ site.Params.navigation_button.label }}
        </a>
      {{ end }}
    </div>
  </nav>
</header>
