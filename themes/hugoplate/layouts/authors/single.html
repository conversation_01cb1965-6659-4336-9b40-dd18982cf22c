{{ define "main" }}
  <section class="section-sm pb-0">
    <div class="container">
      <div
        class="row border-border dark:border-darkmode-border justify-center border-b pb-14">
        <div class="lg:col-4 text-center">
          {{ $image:= .Params.image }}
          {{ if $image }}
            {{ partial "image" (dict "Src" $image "Context" .Page "Alt" .Title "Class" "mx-auto" "Size" "200x200") }}
          {{ else if .Params.Email }}
            <img
              class="mx-auto"
              alt="{{ .Title }}"
              height="200"
              width="200"
              src="https://www.gravatar.com/avatar/{{ md5 .Params.email }}?s=128&pg&d=identicon" />
          {{ end }}
          <h1 class="h3 mt-10">{{ .Title }}</h1>
          <div class="content mt-6">
            {{ .Content }}
          </div>
          <ul class="social-icons ml-4 mt-10 text-center">
            {{ range .Params.social }}
              <li>
                <a
                  href="{{ .link | safeURL }}"
                  target="_blank"
                  rel="noopener nofollow">
                  <span class="sr-only">{{ .title }}</span>
                  <i class="{{ .icon }}"></i>
                </a>
              </li>
            {{ end }}
          </ul>
        </div>
      </div>

      <div class="row pb-16 pt-14">
        {{ $filterByAuthor := where site.RegularPages "Params.author" "==" .Title }}
        {{ range $filterByAuthor }}
          <div class="md:col-6 lg:col-4 mb-12">
            {{ partial "components/blog-card" . }}
          </div>
        {{ end }}
      </div>
    </div>
  </section>
{{ end }}
