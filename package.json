{"name": "hug<PERSON><PERSON>", "description": "hugo tailwindcss boilerplate", "version": "2.0.2", "license": "MIT", "author": "zeon.studio", "scripts": {"dev": "hugo server --bind 0.0.0.0 --port 1313 --baseURL http://localhost:1313", "build": "hugo --gc --minify --templateMetrics --templateMetricsHints --forceSyncStatic", "preview": "hugo server --bind 0.0.0.0 --disableFastRender --navigateToChanged --templateMetrics --templateMetricsHints --watch --forceSyncStatic -e production --minify", "dev:example": "hugo server --bind 0.0.0.0 --port 1313 --baseURL http://localhost:1313", "build:example": "hugo --gc --minify --templateMetrics --templateMetricsHints --forceSyncStatic", "preview:example": "hugo server --bind 0.0.0.0 --disableFastRender --navigateToChanged --templateMetrics --templateMetricsHints --watch --forceSyncStatic -e production --minify", "update-modules": "node ./scripts/clearModules.js && hugo mod clean --all && hugo mod get -u ./... && hugo mod tidy", "remove-darkmode": "node ./scripts/removeDarkmode.js && yarn format", "project-setup": "node ./scripts/projectSetup.js", "theme-setup": "node ./scripts/themeSetup.js", "update-theme": "node ./scripts/themeUpdate.js", "format": "prettier -w ."}, "devDependencies": {"@tailwindcss/cli": "^4.0.6", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "prettier": "^3.5.0", "prettier-plugin-go-template": "0.0.15", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.6"}}