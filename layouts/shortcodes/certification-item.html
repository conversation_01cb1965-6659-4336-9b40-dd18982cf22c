<div class="certification-item bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
  {{ $image := .Get "image" }}
  {{ $title := .Get "title" }}
  {{ $issuer := .Get "issuer" }}
  {{ $date := .Get "date" }}
  {{ $description := .Get "description" }}
  {{ $link := .Get "link" }}
  
  <div class="p-6">
    {{ if $image }}
    <div class="mb-4 flex justify-center">
      <img src="{{ $image }}" alt="{{ $title }}" class="h-24 object-contain">
    </div>
    {{ end }}
    
    <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">{{ $title }}</h3>
    
    <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
      <span class="font-medium">{{ $issuer }}</span>
      {{ if $date }}
      <span class="mx-2">•</span>
      <span>{{ $date }}</span>
      {{ end }}
    </div>
    
    {{ if $description }}
    <p class="text-gray-700 dark:text-gray-300 text-sm mb-4">{{ $description }}</p>
    {{ end }}
    
    {{ if $link }}
    <a href="{{ $link }}" target="_blank" rel="noopener noreferrer" 
       class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm">
      View Certificate
      <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
      </svg>
    </a>
    {{ end }}
  </div>
</div>
