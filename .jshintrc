{"maxerr": 50, "bitwise": true, "camelcase": false, "curly": true, "eqeqeq": true, "forin": true, "freeze": true, "immed": true, "indent": 2, "latedef": true, "newcap": false, "noarg": true, "noempty": true, "nonbsp": true, "nonew": true, "plusplus": false, "undef": true, "unused": false, "strict": true, "maxparams": false, "maxdepth": 4, "maxstatements": false, "maxcomplexity": false, "maxlen": 400, "browser": true, "devel": true, "asi": false, "boss": false, "debug": false, "eqnull": false, "es3": false, "es5": false, "esversion": 12, "moz": false, "evil": true, "expr": true, "funcscope": false, "globalstrict": false, "iterator": false, "lastsemic": false, "laxbreak": false, "laxcomma": false, "loopfunc": true, "multistr": true, "noyield": false, "notypeof": false, "proto": false, "scripturl": false, "shadow": false, "sub": false, "supernew": false, "validthis": false, "globals": {"jQuery": false, "google": false, "$": false}}