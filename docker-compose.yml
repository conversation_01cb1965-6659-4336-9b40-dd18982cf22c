version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: .devcontainer/Dockerfile
    ports:
      - "1313:1313"
      - "3000:3000"
    volumes:
      - .:/app:cached
      - node_modules:/app/node_modules
      - hugo_cache:/tmp/hugo_cache
    environment:
      - NODE_ENV=development
      - HUGO_ENV=development
      - HUGO_CACHEDIR=/tmp/hugo_cache
    command: sh -c "npm install && npm run dev"
    tty: true
    stdin_open: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1313"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  node_modules:
  hugo_cache:
